<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Streaming Chat UI</title>
  <style>
    body { font-family: Arial, sans-serif; padding: 20px; }
    #response {
      white-space: pre-wrap;
      border: 1px solid #ccc;
      padding: 10px;
      min-height: 100px;
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <h2>Ask a Question</h2>
  <input type="text" id="query" placeholder="Type your question..." style="width: 300px;" />
  <button onclick="sendQuery()">Send</button>
  <div id="response"></div>

  <script>
    async function sendQuery() {
      const query = document.getElementById("query").value;
      const responseDiv = document.getElementById("response");
      responseDiv.textContent = "";

      const url = `http://localhost:3001/api/v1/?apikey=test_api_key_1751884336144_vp9gospvg&query=${encodeURIComponent(query)}&stream=true`;

      try {
        const res = await fetch(url, {
          method: "GET",
          headers: {
            "Accept": "text/event-stream"
          }
        });

        const reader = res.body.getReader();
        const decoder = new TextDecoder("utf-8");
        let buffer = '';

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });

          // Process all complete "data:" chunks
          const lines = buffer.split('\n');
          buffer = lines.pop(); // last incomplete line

          for (const line of lines) {
            if (line.startsWith('data:')) {
              try {
                const jsonStr = line.replace(/^data:\s*/, '');
                const dataObj = JSON.parse(jsonStr);

                if (dataObj.type === 'content' && dataObj.content) {
                  responseDiv.textContent += dataObj.content;
                }
              } catch (err) {
                console.error('Failed to parse JSON chunk:', line);
              }
            }
          }
        }
      } catch (err) {
        console.error(err);
        responseDiv.textContent = "Error fetching stream.";
      }
    }
  </script>
</body>
</html>
