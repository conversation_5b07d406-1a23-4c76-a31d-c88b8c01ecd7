<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatAI Rate Limiting Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .chat-input {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        input[type="text"] {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .user-message {
            background: #e3f2fd;
            text-align: right;
        }
        .bot-message {
            background: #f1f8e9;
        }
        .error-message {
            background: #ffebee;
            color: #c62828;
        }
        .rate-limit-message {
            background: #fff3e0;
            color: #ef6c00;
            border-left: 4px solid #ff9800;
        }
        .retry-countdown {
            font-weight: bold;
            color: #ff5722;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 ChatAI Rate Limiting Test</h1>
        <p>This page demonstrates user-friendly rate limiting with auto-retry functionality.</p>
        
        <div class="chat-input">
            <input type="text" id="messageInput" placeholder="Type your message..." />
            <button id="sendButton" onclick="sendMessage()">Send</button>
        </div>
        
        <div class="status" id="status">Ready to send messages. Rate limit: 2 messages per minute.</div>
        
        <div id="messages"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api/v1/';
        const API_KEY = 'test_api_key_1751884336144_vp9gospvg';
        let retryTimeout = null;
        let countdownInterval = null;

        function addMessage(content, type = 'bot') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.innerHTML = content;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function updateStatus(message, isError = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.style.color = isError ? '#c62828' : '#333';
        }

        function setButtonState(disabled, text = 'Send') {
            const button = document.getElementById('sendButton');
            button.disabled = disabled;
            button.textContent = text;
        }

        function startRetryCountdown(seconds) {
            let remaining = seconds;
            
            countdownInterval = setInterval(() => {
                setButtonState(true, `Retry in ${remaining}s`);
                updateStatus(`Rate limited. Auto-retry in ${remaining} seconds...`, true);
                
                remaining--;
                
                if (remaining < 0) {
                    clearInterval(countdownInterval);
                    setButtonState(false, 'Send');
                    updateStatus('Ready to send messages. Rate limit: 2 messages per minute.');
                }
            }, 1000);
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add user message
            addMessage(message, 'user');
            input.value = '';
            
            // Disable button temporarily
            setButtonState(true, 'Sending...');
            updateStatus('Sending message...');
            
            try {
                const response = await fetch(`${API_BASE}?apikey=${API_KEY}&query=${encodeURIComponent(message)}&stream=false`);
                const data = await response.json();
                
                if (response.status === 429) {
                    // Rate limited
                    const userFriendlyMessage = data.userFriendlyMessage || data.message;
                    const retryAfter = data.retryAfter || 60;
                    
                    addMessage(`⏳ ${userFriendlyMessage}`, 'rate-limit');
                    
                    if (data.autoRetry) {
                        startRetryCountdown(retryAfter);
                        
                        // Auto-retry after the specified time
                        retryTimeout = setTimeout(() => {
                            input.value = message; // Restore the message
                            sendMessage(); // Retry automatically
                        }, retryAfter * 1000);
                    } else {
                        setButtonState(false);
                        updateStatus(`Rate limited. Please wait ${Math.ceil(retryAfter / 60)} minutes.`, true);
                    }
                } else if (data.error) {
                    addMessage(`❌ Error: ${data.message}`, 'error');
                    setButtonState(false);
                    updateStatus('Error occurred. Ready to try again.');
                } else {
                    addMessage(data.response, 'bot');
                    setButtonState(false);
                    updateStatus('Message sent successfully. Ready for next message.');
                }
                
            } catch (error) {
                addMessage(`❌ Network Error: ${error.message}`, 'error');
                setButtonState(false);
                updateStatus('Network error. Ready to try again.');
            }
        }

        // Allow Enter key to send message
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Clear any existing timeouts when page unloads
        window.addEventListener('beforeunload', function() {
            if (retryTimeout) clearTimeout(retryTimeout);
            if (countdownInterval) clearInterval(countdownInterval);
        });
    </script>
</body>
</html>
