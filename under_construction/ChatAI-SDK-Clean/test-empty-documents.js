#!/usr/bin/env node

/**
 * Test script for empty documents handling in ChatAI SDK Clean
 * Tests the scenario where key-validator returns empty documents array
 */

const fetch = require('node-fetch');

// Configuration
const CHATAI_SDK_BASE_URL = process.env.CHATAI_SDK_BASE_URL || 'http://localhost:3002';
const TEST_API_KEY = process.env.TEST_API_KEY || 'test_api_key_empty_docs'; // Use a different key that returns empty docs

async function testEmptyDocumentsResponse() {
  console.log('🧪 Testing Empty Documents Handling');
  console.log('===================================\n');

  try {
    const testQuery = 'What is the main topic of the documents?';
    
    console.log(`🌐 Making request to: ${CHATAI_SDK_BASE_URL}/api/v1/`);
    console.log(`🗝️  API Key: ${TEST_API_KEY}`);
    console.log(`📝 Query: ${testQuery}`);
    console.log(`⏱️  Started at: ${new Date().toISOString()}\n`);
    
    const startTime = Date.now();
    
    const response = await fetch(
      `${CHATAI_SDK_BASE_URL}/api/v1/?apikey=${TEST_API_KEY}&query=${encodeURIComponent(testQuery)}&stream=false`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    const duration = Date.now() - startTime;

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Request failed: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    
    if (result.error) {
      throw new Error(`ChatAI-SDK error: ${result.message}`);
    }

    console.log(`✅ Request successful in ${duration}ms`);
    console.log(`🆔 Session ID: ${result.sessionId}`);
    console.log(`📝 Response: "${result.response}"`);
    
    // Verify that the response is the expected static message
    const expectedMessage = "Sorry, I don't have any information regarding this";
    if (result.response === expectedMessage) {
      console.log(`✅ Correct static message returned for empty documents`);
      return true;
    } else {
      console.log(`❌ Expected: "${expectedMessage}"`);
      console.log(`❌ Got: "${result.response}"`);
      return false;
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

async function testEmptyDocumentsStreamingResponse() {
  console.log('\n🧪 Testing Empty Documents Streaming Handling');
  console.log('==============================================\n');

  try {
    const testQuery = 'Tell me about the documents';
    
    console.log(`🌐 Making streaming request to: ${CHATAI_SDK_BASE_URL}/api/v1/`);
    console.log(`📝 Query: ${testQuery}`);
    console.log(`🔄 Stream: true\n`);
    
    const response = await fetch(
      `${CHATAI_SDK_BASE_URL}/api/v1/?apikey=${TEST_API_KEY}&query=${encodeURIComponent(testQuery)}&stream=true`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`Streaming request failed: ${response.status}`);
    }

    console.log(`✅ Streaming response started`);
    console.log(`📡 Content-Type: ${response.headers.get('content-type')}`);
    
    // Read the streaming response
    let streamContent = '';
    let hasStaticMessage = false;
    
    const reader = response.body;
    reader.on('data', (chunk) => {
      const data = chunk.toString();
      streamContent += data;
      
      // Check if the static message is in the stream
      if (data.includes("Sorry, I don't have any information regarding this")) {
        hasStaticMessage = true;
      }
    });

    return new Promise((resolve) => {
      reader.on('end', () => {
        console.log(`📄 Stream content received`);
        if (hasStaticMessage) {
          console.log(`✅ Static message found in streaming response`);
          resolve(true);
        } else {
          console.log(`❌ Static message not found in streaming response`);
          console.log(`📄 Stream content: ${streamContent.substring(0, 200)}...`);
          resolve(false);
        }
      });

      reader.on('error', (error) => {
        console.error('❌ Stream error:', error.message);
        resolve(false);
      });
    });

  } catch (error) {
    console.error('❌ Streaming test failed:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting Empty Documents Handling Tests\n');
  
  const test1Success = await testEmptyDocumentsResponse();
  const test2Success = await testEmptyDocumentsStreamingResponse();
  
  console.log('\n📋 Test Summary');
  console.log('================');
  console.log(`Empty docs response: ${test1Success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Empty docs streaming: ${test2Success ? '✅ PASS' : '❌ FAIL'}`);
  
  if (test1Success && test2Success) {
    console.log('\n🎉 All empty documents tests passed!');
    console.log('✅ Static message handling is working correctly.');
  } else {
    console.log('\n❌ Some tests failed. Please check the implementation.');
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testEmptyDocumentsResponse,
  testEmptyDocumentsStreamingResponse
};
