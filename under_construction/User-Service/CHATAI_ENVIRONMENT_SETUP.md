# ChatAI Environment Configuration

## Required Environment Variables

For the ChatAI service to work with the complete document processing pipeline, you need to set the following environment variables:

### 🔑 **API Keys (Required for Full Functionality)**

```bash
# LlamaCloud API Key for document parsing and vector indexing
LLAMA_CLOUD_API_KEY=llx-your-llamacloud-api-key-here

# Note: OpenRouter API Key moved to ChatAI-SDK-Clean service
# OPENROUTER_API_KEY is no longer needed in User-Service
```

### 📋 **How to Get API Keys**

#### LlamaCloud API Key:

1. Visit [LlamaIndex Cloud](https://cloud.llamaindex.ai/)
2. Sign up or log in to your account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key (starts with `llx-`)

#### OpenRouter API Key:

**Note**: OpenRouter API key is no longer needed in User-Service.
AI chat functionality has been moved to ChatAI-SDK-Clean service.
Configure OpenRouter API key in ChatAI-SDK-Clean instead.

### 🚀 **Setting Environment Variables**

#### For Local Development:

The `.env` file is already configured in the project root with working API keys:

```bash
#ChatAI Service API Keys
LLAMA_CLOUD_API_KEY=llx-0JczdaEIGFyOG4H4PpapuhA4iZrCWn9KPvNDovijga3m6ZbW
# OPENROUTER_API_KEY moved to ChatAI-SDK-Clean service
```

**✅ Ready to Use**: The API keys are already configured and working!

#### For Production Deployment:

If deploying to a different environment, set these environment variables:

- `LLAMA_CLOUD_API_KEY` = your LlamaCloud API key
- Note: `OPENROUTER_API_KEY` is now configured in ChatAI-SDK-Clean service

### ✅ **Verification**

The User-Service will automatically check for the API key on startup:

- **LlamaCloud**: Used for document parsing (PDF, Office files) and vector indexing

If the key is missing, the service will:

- Log warnings about missing functionality
- Gracefully degrade (basic text processing without vector indexing)
- Continue to work for other features

**Note**: AI chat responses are now handled by ChatAI-SDK-Clean service.

### 🔧 **Service Architecture**

With proper API keys configured, the ChatAI service provides:

1. **Document Upload & Processing**
   - File validation and security checks
   - LlamaParse for complex document parsing
   - Vector indexing with LlamaCloud
   - AI-powered document summarization

2. **Chat System**
   - RAG (Retrieval Augmented Generation)
   - Real-time streaming responses
   - Context from uploaded documents
   - Chat history management

3. **Security & Rate Limiting**
   - Per-user upload limits
   - AI usage tracking
   - Comprehensive error handling
   - Secure logging

### 📊 **Monitoring**

The service provides endpoints to monitor usage:

- `/users/app/chatai/get-credit-usage` - AI usage statistics
- Built-in rate limiting and abuse detection
- Comprehensive logging for debugging

### 🛡️ **Security Notes**

- API keys are stored as environment variables (never in code)
- All errors are sanitized before sending to clients
- Rate limiting prevents abuse
- File uploads are validated for security
- Comprehensive logging for audit trails

---

**Note**: The ChatAI service follows the exact same architecture as the reference server implementation, ensuring consistency and reliability.
